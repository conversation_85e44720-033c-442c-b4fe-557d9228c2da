#!/usr/bin/env python
"""
Test script to verify that cancelled mentions are properly excluded from calendar queries
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'appradio.settings')
django.setup()

from apps.mentions.models import MentionReading, Mention
from datetime import date, datetime

def test_cancelled_mentions():
    """Test that cancelled mentions are excluded from calendar queries"""
    
    print("=== Testing Cancelled Mentions Filter ===")
    
    # Get today's date
    today = date.today()
    
    # Test 1: Check all mentions for today
    all_readings = MentionReading.objects.filter(scheduled_date=today)
    print(f"Total readings for today: {all_readings.count()}")
    
    # Test 2: Check mentions excluding cancelled
    non_cancelled_readings = MentionReading.objects.filter(
        scheduled_date=today
    ).exclude(mention__status='cancelled')
    print(f"Non-cancelled readings for today: {non_cancelled_readings.count()}")
    
    # Test 3: Check only cancelled mentions
    cancelled_readings = MentionReading.objects.filter(
        scheduled_date=today,
        mention__status='cancelled'
    )
    print(f"Cancelled readings for today: {cancelled_readings.count()}")
    
    # Test 4: Show details of cancelled mentions
    if cancelled_readings.exists():
        print("\n=== Cancelled Mentions Details ===")
        for reading in cancelled_readings:
            print(f"ID: {reading.id}, Mention: {reading.mention.content[:50]}..., Status: {reading.mention.status}")
    
    # Test 5: Check mention statuses
    print("\n=== Mention Status Distribution ===")
    statuses = Mention.objects.values('status').distinct()
    for status in statuses:
        count = Mention.objects.filter(status=status['status']).count()
        print(f"Status '{status['status']}': {count} mentions")
    
    # Test 6: Check if there are any readings with cancelled mentions
    readings_with_cancelled_mentions = MentionReading.objects.filter(
        mention__status='cancelled'
    ).count()
    print(f"\nTotal readings with cancelled mentions: {readings_with_cancelled_mentions}")
    
    return {
        'total_readings': all_readings.count(),
        'non_cancelled_readings': non_cancelled_readings.count(),
        'cancelled_readings': cancelled_readings.count()
    }

if __name__ == "__main__":
    results = test_cancelled_mentions()
    
    print("\n=== Summary ===")
    print(f"Total readings: {results['total_readings']}")
    print(f"Non-cancelled readings: {results['non_cancelled_readings']}")
    print(f"Cancelled readings: {results['cancelled_readings']}")
    
    if results['cancelled_readings'] > 0:
        print(f"\n⚠️  Found {results['cancelled_readings']} cancelled readings that should be hidden from calendar!")
    else:
        print("\n✅ No cancelled readings found - filter should be working correctly.")
